'use client';

import { CoinbaseWalletConnect } from '@/components/shared/coinbase-wallet-connect';
import { useCoinbaseWallet } from '@/lib/coinbase-wallet-provider';

export default function TestWalletPage() {
  const { isConnected, address, error } = useCoinbaseWallet();

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Wallet Test Page</h1>
      
      <div className="space-y-4">
        <div>
          <strong>Connection Status:</strong> {isConnected ? 'Connected' : 'Not Connected'}
        </div>
        
        {address && (
          <div>
            <strong>Address:</strong> {address}
          </div>
        )}
        
        {error && (
          <div className="text-red-500">
            <strong>Error:</strong> {error}
          </div>
        )}
        
        <div>
          <CoinbaseWalletConnect />
        </div>
      </div>
    </div>
  );
}
